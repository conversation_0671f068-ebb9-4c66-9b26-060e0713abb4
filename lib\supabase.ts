import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types for our fragrance data
export interface Fragrance {
  id: string
  name: string
  brand: string
  release_year?: number
  gender?: string
  top_notes?: string
  middle_notes?: string
  base_notes?: string
  main_accords?: string
  image_url?: string
  is_user_submitted: boolean
  needs_review: boolean
  submitted_by?: string
  created_at: string
  updated_at: string
  similarity_score?: number
}

// Search function that uses our database search_fragrances function
export async function searchFragrances(
  searchTerm: string,
  limit: number = 10
): Promise<{ data: Fragrance[] | null; error: any }> {
  try {
    const { data, error } = await supabase.rpc('search_fragrances', {
      search_term: searchTerm,
      limit_count: limit,
      offset_count: 0
    })

    return { data, error }
  } catch (error) {
    console.error('Error searching fragrances:', error)
    return { data: null, error }
  }
}

// Add user-submitted fragrance
export async function addUserFragrance(
  name: string,
  brand: string,
  imageUrl?: string,
  releaseYear?: number,
  gender?: string
): Promise<{ data: string | null; error: any }> {
  try {
    const { data, error } = await supabase.rpc('add_user_fragrance', {
      fragrance_name: name,
      fragrance_brand: brand,
      fragrance_image_url: imageUrl,
      fragrance_release_year: releaseYear,
      fragrance_gender: gender
    })

    return { data, error }
  } catch (error) {
    console.error('Error adding user fragrance:', error)
    return { data: null, error }
  }
}
