# Fragrance Mood Match – Product Requirements Document (PRD)

## 📌 Overview

**Fragrance Mood Match** is a mobile/web app that recommends personalized fragrances based on the user’s current **season**, **emotion**, and **outfit color**. The app uses a rule-based engine to match user input to curated fragrance profiles, offering 2–3 product suggestions per result.

---

## 🧭 Objective

Allow users to:
- Select from 3 simple inputs: **Season**, **Emotion**, and **Outfit Color**
- Receive curated **fragrance recommendations**
- Discover layering tips and explore products that suit their **mood**, **style**, and **time of year**

---

## 🎯 Target Users

- Fragrance enthusiasts seeking daily inspiration
- Casual users who want fragrance recommendations based on how they feel or look
- People interested in seasonal scent curation

---

## 🖥️ Platform

- MVP: **Responsive web app**
- Optional future: **iOS/Android app (React Native or Flutter)**

---

## 🔧 Core Features

### 1. User Input
Three required dropdown fields:
- **Season**: `Spring`, `Summer`, `Fall`, `Winter`
- **Emotion**: `Confident`, `Romantic`, `Relaxed`, `Playful`, `Melancholy`, `Focused`
- **Outfit Color**: `Black`, `White`, `Red`, `Blue`, `Green`, `Pastel`, `Bright`, `Earth Tones`

### 2. Rule-Based Recommendation Engine
- Matches user input (season + emotion + color) to a **fragrance profile** (style + notes)
- Example key: `summer_romantic_pastel` → returns a fragrance profile

### 3. Fragrance Output
- 2–3 fragrance product suggestions
- Display product name, brand, key notes, and style
- Optional layering or styling tip

---

## 🧠 Recommendation Logic

### Input Mapping
Each combination of inputs maps to a unique profile, e.g.:

```json
"spring_romantic_pastel": {
  "styles": ["floral", "fruity"],
  "notes": ["peony", "rose", "pear", "white musk"]
}


Matching Logic
Fragrances are selected if:

They match at least one style in the profile

OR contain two or more matching notes

OR are tagged with matching season or mood

🗃️ Data Requirements
A. rules.json
Maps all valid combinations to fragrance profiles.

Example:

json
Copy
Edit
{
  "fall_confident_black": {
    "styles": ["woody", "spicy", "leather"],
    "notes": ["saffron", "cedar", "leather", "amber"]
  }
}
B. fragrances.json
Fragrance product database with metadata.

Example:

json
Copy
Edit
{
  "name": "Miss Dior Blooming Bouquet",
  "brand": "Dior",
  "notes": ["peony", "rose", "white musk"],
  "styles": ["floral", "romantic"],
  "seasons": ["spring", "summer"],
  "moods": ["romantic", "playful"],
  "colors": ["pastel", "white", "pink"]
}
📱 UI / UX Requirements
Input Screen
3 dropdowns: Season, Emotion, Outfit Color

“Find My Fragrance” button

Result Screen
“Your Fragrance Match” header

Card layout with:

Fragrance name and brand

Notes and style

Optional: image, layering tip

📦 Technical Overview
Backend (Optional for MVP)
Static JSON data (rules.json, fragrances.json)

Matching logic in frontend

Frontend Stack (Suggested)
React / React Native

Tailwind or Chakra UI

JSON data fetch/match on client side

🧪 MVP Scope
Feature	Included
3-input UI (season, emotion, color)	✅
Rule-based fragrance match	✅
Sample fragrance DB (20–30 entries)	✅
Static UI w/ basic styling	✅
Save/share feature	❌
User accounts / favorites	❌
AI-generated copy or weather data	❌

🧰 Developer Task Breakdown
Data
 Create rules.json with mapped combinations (20–30)

 Build fragrances.json with sample perfumes

Logic
 Implement key generation logic (e.g. spring_confident_black)

 Match profile to fragrance DB

 Return top 2–3 matches

UI
 Input form with 3 dropdowns

 Results view with fragrance cards