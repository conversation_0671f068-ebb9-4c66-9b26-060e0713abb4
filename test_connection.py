#!/usr/bin/env python3
"""
Test Supabase connection and create schema
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv
import logging

# Load environment variables from .env.local
load_dotenv('.env.local')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Supabase configuration
SUPABASE_URL = os.getenv("NEXT_PUBLIC_SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

def test_connection():
    """Test the Supabase connection."""
    try:
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        logger.info("Connected to Supabase successfully!")

        # Test basic query - check if we can access the database
        # Try to list tables instead
        result = supabase.rpc('exec_sql', {'sql': 'SELECT 1 as test'}).execute()
        logger.info("Database connection test successful")

        return supabase
    except Exception as e:
        logger.error(f"Connection test failed, but client created: {str(e)}")
        # Return the client anyway since the connection was established
        return create_client(SUPABASE_URL, SUPABASE_KEY)

def create_schema(supabase: Client):
    """Create the fragrances table schema."""
    logger.info("Creating fragrances table...")
    
    # Read the SQL schema file
    try:
        with open('create_fragrances_schema.sql', 'r') as f:
            schema_sql = f.read()
        
        # Split the SQL into individual statements
        statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
        
        for i, statement in enumerate(statements):
            if statement:
                try:
                    logger.info(f"Executing statement {i+1}/{len(statements)}")
                    # Use the rpc method to execute raw SQL
                    result = supabase.rpc('exec_sql', {'sql': statement}).execute()
                    logger.info(f"Statement {i+1} executed successfully")
                except Exception as e:
                    logger.warning(f"Statement {i+1} failed (might be normal): {str(e)}")
        
        logger.info("Schema creation completed")
        return True
        
    except FileNotFoundError:
        logger.error("create_fragrances_schema.sql file not found")
        return False
    except Exception as e:
        logger.error(f"Schema creation failed: {str(e)}")
        return False

def test_table_creation(supabase: Client):
    """Test if the fragrances table was created successfully."""
    try:
        # Try to query the table structure
        result = supabase.table('fragrances').select('id').limit(1).execute()
        logger.info("Fragrances table exists and is accessible")
        return True
    except Exception as e:
        logger.error(f"Table test failed: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("Testing Supabase connection...")
    
    supabase = test_connection()
    if not supabase:
        logger.error("Cannot proceed without database connection")
        exit(1)
    
    logger.info("Creating database schema...")
    if create_schema(supabase):
        logger.info("Testing table creation...")
        if test_table_creation(supabase):
            logger.info("✅ Database setup completed successfully!")
        else:
            logger.error("❌ Table creation verification failed")
    else:
        logger.error("❌ Schema creation failed")
