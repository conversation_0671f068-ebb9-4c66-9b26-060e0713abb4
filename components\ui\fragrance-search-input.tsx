"use client"

import { useState, useRef, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { useFragranceSearch } from "@/hooks/use-fragrance-search"
import { type Fragrance } from "@/lib/supabase"
import { Search, Loader2, Plus, Calendar, User } from "lucide-react"
import { cn } from "@/lib/utils"

interface FragranceSearchInputProps {
  label: string
  placeholder: string
  value: string
  onValueChange: (value: string) => void
  onFragranceSelect?: (fragrance: Fragrance) => void
  className?: string
  disabled?: boolean
}

export function FragranceSearchInput({
  label,
  placeholder,
  value,
  onValueChange,
  onFragranceSelect,
  className,
  disabled = false
}: FragranceSearchInputProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedFragrance, setSelectedFragrance] = useState<Fragrance | null>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const {
    suggestions,
    isLoading,
    error,
    searchTerm,
    setSearchTerm,
    clearSuggestions
  } = useFragranceSearch()

  // Sync external value with internal search term
  useEffect(() => {
    if (value !== searchTerm) {
      setSearchTerm(value)
    }
  }, [value, searchTerm, setSearchTerm])

  // Handle input changes
  const handleInputChange = (newValue: string) => {
    onValueChange(newValue)
    setSearchTerm(newValue)
    setSelectedFragrance(null)
    setIsOpen(true)
  }

  // Handle fragrance selection
  const handleFragranceSelect = (fragrance: Fragrance) => {
    const displayValue = `${fragrance.name} by ${fragrance.brand}`
    onValueChange(displayValue)
    setSelectedFragrance(fragrance)
    setIsOpen(false)
    clearSuggestions()
    
    if (onFragranceSelect) {
      onFragranceSelect(fragrance)
    }
  }

  // Handle clicking outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsOpen(false)
    }
  }

  const showDropdown = isOpen && (suggestions.length > 0 || isLoading || error)

  return (
    <div className={cn("relative", className)}>
      <Label htmlFor={label.toLowerCase().replace(/\s+/g, '-')}>{label}</Label>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          ref={inputRef}
          id={label.toLowerCase().replace(/\s+/g, '-')}
          placeholder={placeholder}
          value={value}
          onChange={(e) => handleInputChange(e.target.value)}
          onFocus={() => setIsOpen(true)}
          onKeyDown={handleKeyDown}
          className="pl-10"
          disabled={disabled}
        />
        {isLoading && (
          <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-gray-400" />
        )}
      </div>

      {/* Selected fragrance info */}
      {selectedFragrance && (
        <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-md">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-800">
                Found in database: {selectedFragrance.name}
              </p>
              <p className="text-xs text-green-600">
                by {selectedFragrance.brand}
                {selectedFragrance.release_year && ` (${selectedFragrance.release_year})`}
              </p>
            </div>
            <Badge variant="secondary" className="text-xs">
              Existing
            </Badge>
          </div>
        </div>
      )}

      {/* Dropdown with suggestions */}
      {showDropdown && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-64 overflow-y-auto"
        >
          {isLoading && (
            <div className="p-3 text-center text-sm text-gray-500">
              <Loader2 className="h-4 w-4 animate-spin mx-auto mb-2" />
              Searching fragrances...
            </div>
          )}

          {error && (
            <div className="p-3 text-center text-sm text-red-500">
              {error}
            </div>
          )}

          {!isLoading && !error && suggestions.length === 0 && searchTerm.length >= 2 && (
            <div className="p-3 text-center text-sm text-gray-500">
              <div className="flex flex-col items-center space-y-2">
                <Plus className="h-4 w-4" />
                <span>No fragrances found</span>
                <span className="text-xs">You can add this as a new fragrance</span>
              </div>
            </div>
          )}

          {suggestions.map((fragrance) => (
            <button
              key={fragrance.id}
              onClick={() => handleFragranceSelect(fragrance)}
              className="w-full p-3 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0 focus:outline-none focus:bg-gray-50"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {fragrance.name}
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    by {fragrance.brand}
                  </p>
                  <div className="flex items-center space-x-3 mt-1">
                    {fragrance.release_year && (
                      <div className="flex items-center text-xs text-gray-400">
                        <Calendar className="h-3 w-3 mr-1" />
                        {fragrance.release_year}
                      </div>
                    )}
                    {fragrance.gender && (
                      <div className="flex items-center text-xs text-gray-400">
                        <User className="h-3 w-3 mr-1" />
                        {fragrance.gender}
                      </div>
                    )}
                  </div>
                  {fragrance.main_accords && (
                    <p className="text-xs text-gray-400 mt-1 truncate">
                      {fragrance.main_accords}
                    </p>
                  )}
                </div>
                <div className="ml-2 flex-shrink-0">
                  {fragrance.is_user_submitted ? (
                    <Badge variant="outline" className="text-xs">
                      User Added
                    </Badge>
                  ) : (
                    <Badge variant="secondary" className="text-xs">
                      Database
                    </Badge>
                  )}
                </div>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  )
}
