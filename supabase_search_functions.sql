-- MyScentSpace Search Functions
-- Execute this script after creating the main schema

-- 1. Function to search fragrances by name and brand
CREATE OR REPLACE FUNCTION search_fragrances(
    search_term TEXT DEFAULT '',
    limit_count INTEGER DEFAULT 50,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    name TEXT,
    brand TEXT,
    release_year INTEGER,
    gender TEXT,
    top_notes TEXT,
    middle_notes TEXT,
    base_notes TEXT,
    main_accords TEXT,
    image_url TEXT,
    is_user_submitted BOOLEAN,
    needs_review BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    similarity_score REAL
) 
LANGUAGE plpgsql
AS $$
BEGIN
    IF search_term = '' OR search_term IS NULL THEN
        RETURN QUERY
        SELECT 
            f.id,
            f.name,
            f.brand,
            f.release_year,
            f.gender,
            f.top_notes,
            f.middle_notes,
            f.base_notes,
            f.main_accords,
            f.image_url,
            f.is_user_submitted,
            f.needs_review,
            f.created_at,
            0.0::REAL as similarity_score
        FROM fragrances f
        ORDER BY f.name, f.brand
        LIMIT limit_count OFFSET offset_count;
    ELSE
        RETURN QUERY
        SELECT 
            f.id,
            f.name,
            f.brand,
            f.release_year,
            f.gender,
            f.top_notes,
            f.middle_notes,
            f.base_notes,
            f.main_accords,
            f.image_url,
            f.is_user_submitted,
            f.needs_review,
            f.created_at,
            (
                ts_rank(to_tsvector('english', f.name || ' ' || f.brand), plainto_tsquery('english', search_term)) +
                CASE WHEN f.name ILIKE '%' || search_term || '%' THEN 0.5 ELSE 0 END +
                CASE WHEN f.brand ILIKE '%' || search_term || '%' THEN 0.3 ELSE 0 END
            )::REAL as similarity_score
        FROM fragrances f
        WHERE 
            to_tsvector('english', f.name || ' ' || f.brand) @@ plainto_tsquery('english', search_term)
            OR f.name ILIKE '%' || search_term || '%'
            OR f.brand ILIKE '%' || search_term || '%'
        ORDER BY similarity_score DESC, f.name, f.brand
        LIMIT limit_count OFFSET offset_count;
    END IF;
END;
$$;

-- 2. Function to search fragrances by notes
CREATE OR REPLACE FUNCTION search_fragrances_by_notes(
    search_notes TEXT,
    limit_count INTEGER DEFAULT 50
)
RETURNS TABLE (
    id UUID,
    name TEXT,
    brand TEXT,
    top_notes TEXT,
    middle_notes TEXT,
    base_notes TEXT,
    main_accords TEXT,
    note_match_score REAL
) 
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        f.id,
        f.name,
        f.brand,
        f.top_notes,
        f.middle_notes,
        f.base_notes,
        f.main_accords,
        (
            CASE WHEN f.top_notes ILIKE '%' || search_notes || '%' THEN 1.0 ELSE 0 END +
            CASE WHEN f.middle_notes ILIKE '%' || search_notes || '%' THEN 0.8 ELSE 0 END +
            CASE WHEN f.base_notes ILIKE '%' || search_notes || '%' THEN 0.8 ELSE 0 END +
            CASE WHEN f.main_accords ILIKE '%' || search_notes || '%' THEN 0.6 ELSE 0 END
        )::REAL as note_match_score
    FROM fragrances f
    WHERE 
        f.top_notes ILIKE '%' || search_notes || '%'
        OR f.middle_notes ILIKE '%' || search_notes || '%'
        OR f.base_notes ILIKE '%' || search_notes || '%'
        OR f.main_accords ILIKE '%' || search_notes || '%'
    ORDER BY note_match_score DESC, f.name
    LIMIT limit_count;
END;
$$;

-- 3. Function to add user-submitted fragrance
CREATE OR REPLACE FUNCTION add_user_fragrance(
    fragrance_name TEXT,
    fragrance_brand TEXT,
    fragrance_image_url TEXT DEFAULT NULL,
    fragrance_release_year INTEGER DEFAULT NULL,
    fragrance_gender TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    new_fragrance_id UUID;
BEGIN
    -- Check if user is authenticated
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated';
    END IF;
    
    -- Insert the new fragrance
    INSERT INTO fragrances (
        name,
        brand,
        release_year,
        gender,
        image_url,
        is_user_submitted,
        needs_review,
        submitted_by
    ) VALUES (
        fragrance_name,
        fragrance_brand,
        fragrance_release_year,
        fragrance_gender,
        fragrance_image_url,
        true,
        true,
        auth.uid()
    ) RETURNING id INTO new_fragrance_id;
    
    RETURN new_fragrance_id;
END;
$$;

-- 4. Function to get fragrances needing review (for admins)
CREATE OR REPLACE FUNCTION get_fragrances_for_review(
    limit_count INTEGER DEFAULT 50
)
RETURNS TABLE (
    id UUID,
    name TEXT,
    brand TEXT,
    release_year INTEGER,
    gender TEXT,
    image_url TEXT,
    submitted_by UUID,
    created_at TIMESTAMP WITH TIME ZONE
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Note: Add admin role check here when implementing admin system
    RETURN QUERY
    SELECT 
        f.id,
        f.name,
        f.brand,
        f.release_year,
        f.gender,
        f.image_url,
        f.submitted_by,
        f.created_at
    FROM fragrances f
    WHERE f.needs_review = true
    ORDER BY f.created_at DESC
    LIMIT limit_count;
END;
$$;
