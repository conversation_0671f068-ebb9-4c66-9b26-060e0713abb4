-- MyScentSpace Fragrances Database Schema
-- Run this in your Supabase SQL Editor first

-- 1. Create the fragrances table
CREATE TABLE IF NOT EXISTS fragrances (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    brand TEXT NOT NULL,
    release_year INTEGER,
    gender TEXT,
    top_notes TEXT,
    middle_notes TEXT,
    base_notes TEXT,
    main_accords TEXT,
    image_url TEXT,
    is_user_submitted BOOLEAN DEFAULT FALSE,
    needs_review BOOLEAN DEFAULT FALSE,
    submitted_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create indexes for efficient searching
CREATE INDEX IF NOT EXISTS idx_fragrances_name ON fragrances USING gin(to_tsvector('english', name));
CREATE INDEX IF NOT EXISTS idx_fragrances_brand ON fragrances USING gin(to_tsvector('english', brand));
CREATE INDEX IF NOT EXISTS idx_fragrances_name_brand ON fragrances USING gin(to_tsvector('english', name || ' ' || brand));
CREATE INDEX IF NOT EXISTS idx_fragrances_top_notes ON fragrances USING gin(to_tsvector('english', COALESCE(top_notes, '')));
CREATE INDEX IF NOT EXISTS idx_fragrances_middle_notes ON fragrances USING gin(to_tsvector('english', COALESCE(middle_notes, '')));
CREATE INDEX IF NOT EXISTS idx_fragrances_base_notes ON fragrances USING gin(to_tsvector('english', COALESCE(base_notes, '')));
CREATE INDEX IF NOT EXISTS idx_fragrances_main_accords ON fragrances USING gin(to_tsvector('english', COALESCE(main_accords, '')));
CREATE INDEX IF NOT EXISTS idx_fragrances_gender ON fragrances(gender);
CREATE INDEX IF NOT EXISTS idx_fragrances_release_year ON fragrances(release_year);
CREATE INDEX IF NOT EXISTS idx_fragrances_user_submitted ON fragrances(is_user_submitted);
CREATE INDEX IF NOT EXISTS idx_fragrances_needs_review ON fragrances(needs_review);

-- 3. Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 4. Create trigger to automatically update updated_at
DROP TRIGGER IF EXISTS update_fragrances_updated_at ON fragrances;
CREATE TRIGGER update_fragrances_updated_at 
    BEFORE UPDATE ON fragrances 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 5. Enable Row Level Security (RLS)
ALTER TABLE fragrances ENABLE ROW LEVEL SECURITY;

-- 6. Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Allow public read access" ON fragrances;
DROP POLICY IF EXISTS "Allow authenticated users to insert" ON fragrances;
DROP POLICY IF EXISTS "Allow users to update own submissions" ON fragrances;

-- 7. Create RLS policies
CREATE POLICY "Allow public read access" ON fragrances
    FOR SELECT USING (true);

CREATE POLICY "Allow authenticated users to insert" ON fragrances
    FOR INSERT WITH CHECK (
        auth.role() = 'authenticated' AND 
        is_user_submitted = true AND
        submitted_by = auth.uid()
    );

CREATE POLICY "Allow users to update own submissions" ON fragrances
    FOR UPDATE USING (
        auth.role() = 'authenticated' AND 
        is_user_submitted = true AND
        submitted_by = auth.uid()
    );

-- 8. Search function for fragrances
CREATE OR REPLACE FUNCTION search_fragrances(
    search_term TEXT DEFAULT '',
    limit_count INTEGER DEFAULT 50,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    name TEXT,
    brand TEXT,
    release_year INTEGER,
    gender TEXT,
    top_notes TEXT,
    middle_notes TEXT,
    base_notes TEXT,
    main_accords TEXT,
    image_url TEXT,
    is_user_submitted BOOLEAN,
    needs_review BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    similarity_score REAL
) 
LANGUAGE plpgsql
AS $$
BEGIN
    IF search_term = '' OR search_term IS NULL THEN
        RETURN QUERY
        SELECT 
            f.id,
            f.name,
            f.brand,
            f.release_year,
            f.gender,
            f.top_notes,
            f.middle_notes,
            f.base_notes,
            f.main_accords,
            f.image_url,
            f.is_user_submitted,
            f.needs_review,
            f.created_at,
            0.0::REAL as similarity_score
        FROM fragrances f
        ORDER BY f.name, f.brand
        LIMIT limit_count OFFSET offset_count;
    ELSE
        RETURN QUERY
        SELECT 
            f.id,
            f.name,
            f.brand,
            f.release_year,
            f.gender,
            f.top_notes,
            f.middle_notes,
            f.base_notes,
            f.main_accords,
            f.image_url,
            f.is_user_submitted,
            f.needs_review,
            f.created_at,
            (
                ts_rank(to_tsvector('english', f.name || ' ' || f.brand), plainto_tsquery('english', search_term)) +
                CASE WHEN f.name ILIKE '%' || search_term || '%' THEN 0.5 ELSE 0 END +
                CASE WHEN f.brand ILIKE '%' || search_term || '%' THEN 0.3 ELSE 0 END
            )::REAL as similarity_score
        FROM fragrances f
        WHERE 
            to_tsvector('english', f.name || ' ' || f.brand) @@ plainto_tsquery('english', search_term)
            OR f.name ILIKE '%' || search_term || '%'
            OR f.brand ILIKE '%' || search_term || '%'
        ORDER BY similarity_score DESC, f.name, f.brand
        LIMIT limit_count OFFSET offset_count;
    END IF;
END;
$$;

-- 9. Function to add user-submitted fragrance
CREATE OR REPLACE FUNCTION add_user_fragrance(
    fragrance_name TEXT,
    fragrance_brand TEXT,
    fragrance_image_url TEXT DEFAULT NULL,
    fragrance_release_year INTEGER DEFAULT NULL,
    fragrance_gender TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    new_fragrance_id UUID;
BEGIN
    -- Check if user is authenticated
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated';
    END IF;
    
    -- Insert the new fragrance
    INSERT INTO fragrances (
        name,
        brand,
        release_year,
        gender,
        image_url,
        is_user_submitted,
        needs_review,
        submitted_by
    ) VALUES (
        fragrance_name,
        fragrance_brand,
        fragrance_release_year,
        fragrance_gender,
        fragrance_image_url,
        true,
        true,
        auth.uid()
    ) RETURNING id INTO new_fragrance_id;
    
    RETURN new_fragrance_id;
END;
$$;
