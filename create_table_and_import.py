#!/usr/bin/env python3
"""
Create fragrances table and import data directly using Supabase Python client
"""

import os
import pandas as pd
from supabase import create_client, Client
from dotenv import load_dotenv
import logging
from typing import Dict, Any, List
import time

# Load environment variables from .env.local
load_dotenv('.env.local')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Supabase configuration
SUPABASE_URL = os.getenv("NEXT_PUBLIC_SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_URL or not SUPABASE_KEY:
    raise ValueError("Please check NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in your .env.local file")

def clean_text(text: Any) -> str:
    """Clean and normalize text data."""
    if pd.isna(text) or text == '':
        return None
    return str(text).strip()

def clean_integer(value: Any) -> int:
    """Clean and convert to integer."""
    if pd.isna(value) or value == '':
        return None
    try:
        return int(float(value))
    except (ValueError, TypeError):
        return None

def process_fragrance_row(row: pd.Series) -> Dict[str, Any]:
    """Process a single row from the CSV and return a clean fragrance record."""
    return {
        'name': clean_text(row.get('name')),
        'brand': clean_text(row.get('brand')),
        'release_year': clean_integer(row.get('release_year')),
        'gender': clean_text(row.get('gender')),
        'top_notes': clean_text(row.get('top_notes')),
        'middle_notes': clean_text(row.get('middle_notes')),
        'base_notes': clean_text(row.get('base_notes')),
        'main_accords': clean_text(row.get('main_accords')),
        'is_user_submitted': False,
        'needs_review': False
    }

def test_table_exists(supabase: Client) -> bool:
    """Test if the fragrances table exists."""
    try:
        result = supabase.table('fragrances').select('id').limit(1).execute()
        logger.info("Fragrances table already exists")
        return True
    except Exception as e:
        logger.info(f"Fragrances table doesn't exist: {str(e)}")
        return False

def create_test_record(supabase: Client) -> bool:
    """Create a test record to verify table structure."""
    try:
        test_record = {
            'name': 'Test Fragrance',
            'brand': 'Test Brand',
            'is_user_submitted': False,
            'needs_review': False
        }
        
        result = supabase.table('fragrances').insert(test_record).execute()
        
        # Delete the test record
        supabase.table('fragrances').delete().eq('name', 'Test Fragrance').execute()
        
        logger.info("Table structure verified successfully")
        return True
        
    except Exception as e:
        logger.error(f"Table structure test failed: {str(e)}")
        return False

def batch_insert_fragrances(supabase: Client, fragrances: List[Dict[str, Any]], batch_size: int = 50):
    """Insert fragrances in batches to avoid API limits."""
    total_inserted = 0
    total_errors = 0
    total_batches = (len(fragrances) + batch_size - 1) // batch_size

    for i in range(0, len(fragrances), batch_size):
        batch = fragrances[i:i + batch_size]
        batch_num = i//batch_size + 1
        try:
            result = supabase.table('fragrances').insert(batch).execute()
            total_inserted += len(batch)

            # Progress reporting every 100 batches or at milestones
            if batch_num % 100 == 0 or batch_num in [1, 10, 50] or batch_num == total_batches:
                progress_pct = (batch_num / total_batches) * 100
                logger.info(f"Progress: {progress_pct:.1f}% - Batch {batch_num}/{total_batches}: {len(batch)} fragrances (Total: {total_inserted})")

            # Add a delay to avoid rate limiting
            time.sleep(0.2)
            
        except Exception as e:
            logger.error(f"Error inserting batch {i//batch_size + 1}: {str(e)}")
            
            # Try inserting individual records in this batch
            for fragrance in batch:
                try:
                    supabase.table('fragrances').insert(fragrance).execute()
                    total_inserted += 1
                    time.sleep(0.05)
                except Exception as individual_error:
                    total_errors += 1
                    logger.error(f"Error inserting fragrance '{fragrance.get('name', 'Unknown')}' by '{fragrance.get('brand', 'Unknown')}': {str(individual_error)}")
    
    return total_inserted, total_errors

def remove_duplicates(fragrances: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Remove duplicate fragrances based on name and brand."""
    seen = set()
    unique_fragrances = []
    
    for fragrance in fragrances:
        name = fragrance.get('name', '').lower().strip()
        brand = fragrance.get('brand', '').lower().strip()
        key = (name, brand)
        
        if key not in seen and name and brand:
            seen.add(key)
            unique_fragrances.append(fragrance)
    
    logger.info(f"Removed {len(fragrances) - len(unique_fragrances)} duplicates")
    return unique_fragrances

def main():
    """Main function to import fragrance data."""
    try:
        # Initialize Supabase client
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        logger.info("Connected to Supabase")
        
        # Check if table exists
        if not test_table_exists(supabase):
            logger.error("Fragrances table doesn't exist!")
            logger.info("Please create the table first using the SQL schema in your Supabase dashboard:")
            logger.info("1. Go to your Supabase dashboard")
            logger.info("2. Navigate to SQL Editor")
            logger.info("3. Copy and paste the contents of 'create_fragrances_schema.sql'")
            logger.info("4. Run the SQL script")
            logger.info("5. Then run this script again")
            return
        
        # Test table structure
        if not create_test_record(supabase):
            logger.error("Table structure verification failed")
            return
        
        # Check existing data count
        try:
            result = supabase.table('fragrances').select('id', count='exact').execute()
            existing_count = len(result.data) if result.data else 0
            logger.info(f"Found {existing_count} existing fragrances in database")
            
            if existing_count > 100:
                response = input(f"Database already contains {existing_count} fragrances. Continue with import? (y/N): ")
                if response.lower() != 'y':
                    logger.info("Import cancelled by user")
                    return
        except Exception as e:
            logger.info("Could not check existing data count")
        
        # Read CSV file
        logger.info("Reading CSV file...")
        df = pd.read_csv('consolidated_perfume_data.csv')
        logger.info(f"Loaded {len(df)} rows from CSV")
        
        # Process the data
        logger.info("Processing fragrance data...")
        fragrances = []
        
        for index, row in df.iterrows():
            fragrance = process_fragrance_row(row)
            
            # Only add fragrances with valid name and brand
            if fragrance['name'] and fragrance['brand']:
                fragrances.append(fragrance)
            
            if (index + 1) % 10000 == 0:
                logger.info(f"Processed {index + 1} rows...")
        
        logger.info(f"Processed {len(fragrances)} valid fragrances")
        
        # Remove duplicates
        fragrances = remove_duplicates(fragrances)

        logger.info(f"Ready to import {len(fragrances)} unique fragrances")
        
        # Insert data into Supabase
        logger.info("Starting data import to Supabase...")
        logger.info("This will take approximately 30-45 minutes for all fragrances...")
        total_inserted, total_errors = batch_insert_fragrances(supabase, fragrances, batch_size=50)
        
        logger.info(f"Import completed!")
        logger.info(f"Total inserted: {total_inserted}")
        logger.info(f"Total errors: {total_errors}")
        logger.info(f"Success rate: {(total_inserted / len(fragrances) * 100):.2f}%")
        
        # Test search functionality
        logger.info("Testing search functionality...")
        try:
            search_result = supabase.table('fragrances').select('*').ilike('name', '%Dior%').limit(5).execute()
            logger.info(f"Found {len(search_result.data)} Dior fragrances")
            for fragrance in search_result.data[:3]:
                logger.info(f"  - {fragrance['name']} by {fragrance['brand']}")
        except Exception as e:
            logger.error(f"Search test failed: {str(e)}")
        
    except Exception as e:
        logger.error(f"Import failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
