"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { searchFragrances, type Fragrance } from "@/lib/supabase"

export function TestSearch() {
  const [searchTerm, setSearchTerm] = useState("")
  const [results, setResults] = useState<Fragrance[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSearch = async () => {
    if (!searchTerm.trim()) return

    setIsLoading(true)
    setError(null)

    try {
      const { data, error: searchError } = await searchFragrances(searchTerm, 10)
      
      if (searchError) {
        setError(`Search error: ${searchError.message || 'Unknown error'}`)
        setResults([])
      } else {
        setResults(data || [])
      }
    } catch (err) {
      setError(`Unexpected error: ${err}`)
      setResults([])
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Test Fragrance Search</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex space-x-2">
          <Input
            placeholder="Search for fragrances (e.g., Dior, Sauvage, Tom Ford)"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
          />
          <Button onClick={handleSearch} disabled={isLoading}>
            {isLoading ? 'Searching...' : 'Search'}
          </Button>
        </div>

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md text-red-700">
            {error}
          </div>
        )}

        {results.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-medium">Found {results.length} fragrances:</h3>
            {results.map((fragrance) => (
              <div key={fragrance.id} className="p-3 border rounded-md">
                <div className="font-medium">{fragrance.name}</div>
                <div className="text-sm text-gray-600">by {fragrance.brand}</div>
                {fragrance.release_year && (
                  <div className="text-xs text-gray-500">Released: {fragrance.release_year}</div>
                )}
                {fragrance.main_accords && (
                  <div className="text-xs text-gray-500">Accords: {fragrance.main_accords}</div>
                )}
              </div>
            ))}
          </div>
        )}

        {!isLoading && !error && results.length === 0 && searchTerm && (
          <div className="text-center text-gray-500">
            No fragrances found for "{searchTerm}"
          </div>
        )}
      </CardContent>
    </Card>
  )
}
