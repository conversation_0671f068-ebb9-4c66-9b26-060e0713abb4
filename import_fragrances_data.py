#!/usr/bin/env python3
"""
MyScentSpace Fragrance Data Import Script

This script imports fragrance data from consolidated_perfume_data.csv into Supabase.
Make sure to install required dependencies: pip install supabase pandas python-dotenv

Usage: python import_fragrances_data.py
"""

import os
import pandas as pd
from supabase import create_client, Client
from dotenv import load_dotenv
import logging
from typing import Dict, Any, List
import time

# Load environment variables from .env.local
load_dotenv('.env.local')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Supabase configuration from .env.local
SUPABASE_URL = os.getenv("NEXT_PUBLIC_SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")  # Using service role for admin operations

if not SUPABASE_URL or not SUPABASE_KEY:
    raise ValueError("Please check NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in your .env.local file")

def clean_text(text: Any) -> str:
    """Clean and normalize text data."""
    if pd.isna(text) or text == '':
        return None
    return str(text).strip()

def clean_integer(value: Any) -> int:
    """Clean and convert to integer."""
    if pd.isna(value) or value == '':
        return None
    try:
        return int(float(value))
    except (ValueError, TypeError):
        return None

def process_fragrance_row(row: pd.Series) -> Dict[str, Any]:
    """Process a single row from the CSV and return a clean fragrance record."""
    return {
        'name': clean_text(row.get('name')),
        'brand': clean_text(row.get('brand')),
        'release_year': clean_integer(row.get('release_year')),
        'gender': clean_text(row.get('gender')),
        'top_notes': clean_text(row.get('top_notes')),
        'middle_notes': clean_text(row.get('middle_notes')),
        'base_notes': clean_text(row.get('base_notes')),
        'main_accords': clean_text(row.get('main_accords')),
        'is_user_submitted': False,
        'needs_review': False
    }

def batch_insert_fragrances(supabase: Client, fragrances: List[Dict[str, Any]], batch_size: int = 100):
    """Insert fragrances in batches to avoid API limits."""
    total_inserted = 0
    total_errors = 0
    
    for i in range(0, len(fragrances), batch_size):
        batch = fragrances[i:i + batch_size]
        try:
            result = supabase.table('fragrances').insert(batch).execute()
            total_inserted += len(batch)
            logger.info(f"Inserted batch {i//batch_size + 1}: {len(batch)} fragrances (Total: {total_inserted})")
            
            # Add a small delay to avoid rate limiting
            time.sleep(0.1)
            
        except Exception as e:
            total_errors += len(batch)
            logger.error(f"Error inserting batch {i//batch_size + 1}: {str(e)}")
            
            # Try inserting individual records in this batch
            for fragrance in batch:
                try:
                    supabase.table('fragrances').insert(fragrance).execute()
                    total_inserted += 1
                except Exception as individual_error:
                    total_errors += 1
                    logger.error(f"Error inserting individual fragrance '{fragrance.get('name', 'Unknown')}' by '{fragrance.get('brand', 'Unknown')}': {str(individual_error)}")
    
    return total_inserted, total_errors

def remove_duplicates(fragrances: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Remove duplicate fragrances based on name and brand."""
    seen = set()
    unique_fragrances = []
    
    for fragrance in fragrances:
        name = fragrance.get('name', '').lower().strip()
        brand = fragrance.get('brand', '').lower().strip()
        key = (name, brand)
        
        if key not in seen and name and brand:
            seen.add(key)
            unique_fragrances.append(fragrance)
    
    logger.info(f"Removed {len(fragrances) - len(unique_fragrances)} duplicates")
    return unique_fragrances

def create_database_schema(supabase: Client):
    """Create the database schema and functions."""
    logger.info("Creating database schema...")

    # Create fragrances table
    schema_sql = """
    -- Create the fragrances table
    CREATE TABLE IF NOT EXISTS fragrances (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        name TEXT NOT NULL,
        brand TEXT NOT NULL,
        release_year INTEGER,
        gender TEXT,
        top_notes TEXT,
        middle_notes TEXT,
        base_notes TEXT,
        main_accords TEXT,
        image_url TEXT,
        is_user_submitted BOOLEAN DEFAULT FALSE,
        needs_review BOOLEAN DEFAULT FALSE,
        submitted_by UUID REFERENCES auth.users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    -- Create indexes for efficient searching
    CREATE INDEX IF NOT EXISTS idx_fragrances_name ON fragrances USING gin(to_tsvector('english', name));
    CREATE INDEX IF NOT EXISTS idx_fragrances_brand ON fragrances USING gin(to_tsvector('english', brand));
    CREATE INDEX IF NOT EXISTS idx_fragrances_name_brand ON fragrances USING gin(to_tsvector('english', name || ' ' || brand));
    CREATE INDEX IF NOT EXISTS idx_fragrances_top_notes ON fragrances USING gin(to_tsvector('english', COALESCE(top_notes, '')));
    CREATE INDEX IF NOT EXISTS idx_fragrances_middle_notes ON fragrances USING gin(to_tsvector('english', COALESCE(middle_notes, '')));
    CREATE INDEX IF NOT EXISTS idx_fragrances_base_notes ON fragrances USING gin(to_tsvector('english', COALESCE(base_notes, '')));
    CREATE INDEX IF NOT EXISTS idx_fragrances_main_accords ON fragrances USING gin(to_tsvector('english', COALESCE(main_accords, '')));
    CREATE INDEX IF NOT EXISTS idx_fragrances_gender ON fragrances(gender);
    CREATE INDEX IF NOT EXISTS idx_fragrances_release_year ON fragrances(release_year);
    CREATE INDEX IF NOT EXISTS idx_fragrances_user_submitted ON fragrances(is_user_submitted);
    CREATE INDEX IF NOT EXISTS idx_fragrances_needs_review ON fragrances(needs_review);

    -- Create function to update updated_at timestamp
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
    END;
    $$ language 'plpgsql';

    -- Create trigger to automatically update updated_at
    DROP TRIGGER IF EXISTS update_fragrances_updated_at ON fragrances;
    CREATE TRIGGER update_fragrances_updated_at
        BEFORE UPDATE ON fragrances
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();

    -- Enable Row Level Security (RLS)
    ALTER TABLE fragrances ENABLE ROW LEVEL SECURITY;

    -- Drop existing policies if they exist
    DROP POLICY IF EXISTS "Allow public read access" ON fragrances;
    DROP POLICY IF EXISTS "Allow authenticated users to insert" ON fragrances;
    DROP POLICY IF EXISTS "Allow users to update own submissions" ON fragrances;

    -- Create RLS policies
    CREATE POLICY "Allow public read access" ON fragrances
        FOR SELECT USING (true);

    CREATE POLICY "Allow authenticated users to insert" ON fragrances
        FOR INSERT WITH CHECK (
            auth.role() = 'authenticated' AND
            is_user_submitted = true AND
            submitted_by = auth.uid()
        );

    CREATE POLICY "Allow users to update own submissions" ON fragrances
        FOR UPDATE USING (
            auth.role() = 'authenticated' AND
            is_user_submitted = true AND
            submitted_by = auth.uid()
        );
    """

    try:
        # Execute schema creation using raw SQL
        result = supabase.postgrest.rpc('exec_sql', {'sql': schema_sql}).execute()
        logger.info("Database schema created successfully")
    except Exception as e:
        logger.warning(f"Schema creation might have failed (this is normal if already exists): {str(e)}")

def main():
    """Main function to import fragrance data."""
    try:
        # Initialize Supabase client
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        logger.info("Connected to Supabase")

        # Create database schema
        create_database_schema(supabase)

        # Read CSV file
        logger.info("Reading CSV file...")
        df = pd.read_csv('consolidated_perfume_data.csv')
        logger.info(f"Loaded {len(df)} rows from CSV")
        
        # Process the data
        logger.info("Processing fragrance data...")
        fragrances = []
        
        for index, row in df.iterrows():
            fragrance = process_fragrance_row(row)
            
            # Only add fragrances with valid name and brand
            if fragrance['name'] and fragrance['brand']:
                fragrances.append(fragrance)
            
            if (index + 1) % 10000 == 0:
                logger.info(f"Processed {index + 1} rows...")
        
        logger.info(f"Processed {len(fragrances)} valid fragrances")
        
        # Remove duplicates
        fragrances = remove_duplicates(fragrances)
        
        # Insert data into Supabase
        logger.info("Starting data import to Supabase...")
        total_inserted, total_errors = batch_insert_fragrances(supabase, fragrances)
        
        logger.info(f"Import completed!")
        logger.info(f"Total inserted: {total_inserted}")
        logger.info(f"Total errors: {total_errors}")
        logger.info(f"Success rate: {(total_inserted / len(fragrances) * 100):.2f}%")
        
    except Exception as e:
        logger.error(f"Import failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
