-- Simple fragrances table creation for MyScentSpace
-- Copy and paste this into your Supabase SQL Editor

CREATE TABLE fragrances (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    brand TEXT NOT NULL,
    release_year INTEGER,
    gender TEXT,
    top_notes TEXT,
    middle_notes TEXT,
    base_notes TEXT,
    main_accords TEXT,
    image_url TEXT,
    is_user_submitted BOOLEAN DEFAULT FALSE,
    needs_review BOOLEAN DEFAULT FALSE,
    submitted_by <PERSON><PERSON><PERSON>,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create basic indexes
CREATE INDEX idx_fragrances_name ON fragrances(name);
CREATE INDEX idx_fragrances_brand ON fragrances(brand);
CREATE INDEX idx_fragrances_name_brand ON fragrances(name, brand);

-- Enable RLS
ALTER TABLE fragrances ENABLE ROW LEVEL SECURITY;

-- Allow public read access
CREATE POLICY "Allow public read access" ON fragrances
    FOR SELECT USING (true);
