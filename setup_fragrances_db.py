#!/usr/bin/env python3
"""
MyScentSpace Database Setup and Data Import Script

This script sets up the fragrances database schema and imports data from consolidated_perfume_data.csv
"""

import os
import pandas as pd
from supabase import create_client, Client
from dotenv import load_dotenv
import logging
from typing import Dict, Any, List
import time

# Load environment variables from .env.local
load_dotenv('.env.local')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Supabase configuration
SUPABASE_URL = os.getenv("NEXT_PUBLIC_SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_URL or not SUPABASE_KEY:
    raise ValueError("Please check NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in your .env.local file")

def clean_text(text: Any) -> str:
    """Clean and normalize text data."""
    if pd.isna(text) or text == '':
        return None
    return str(text).strip()

def clean_integer(value: Any) -> int:
    """Clean and convert to integer."""
    if pd.isna(value) or value == '':
        return None
    try:
        return int(float(value))
    except (ValueError, TypeError):
        return None

def process_fragrance_row(row: pd.Series) -> Dict[str, Any]:
    """Process a single row from the CSV and return a clean fragrance record."""
    return {
        'name': clean_text(row.get('name')),
        'brand': clean_text(row.get('brand')),
        'release_year': clean_integer(row.get('release_year')),
        'gender': clean_text(row.get('gender')),
        'top_notes': clean_text(row.get('top_notes')),
        'middle_notes': clean_text(row.get('middle_notes')),
        'base_notes': clean_text(row.get('base_notes')),
        'main_accords': clean_text(row.get('main_accords')),
        'is_user_submitted': False,
        'needs_review': False
    }

def create_table_if_not_exists(supabase: Client):
    """Create the fragrances table if it doesn't exist."""
    logger.info("Creating fragrances table...")
    
    # First, let's check if the table exists
    try:
        result = supabase.table('fragrances').select('id').limit(1).execute()
        logger.info("Fragrances table already exists")
        return True
    except Exception:
        logger.info("Fragrances table doesn't exist, will create it")
    
    # Create table using direct SQL execution
    create_table_sql = """
    CREATE TABLE fragrances (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        name TEXT NOT NULL,
        brand TEXT NOT NULL,
        release_year INTEGER,
        gender TEXT,
        top_notes TEXT,
        middle_notes TEXT,
        base_notes TEXT,
        main_accords TEXT,
        image_url TEXT,
        is_user_submitted BOOLEAN DEFAULT FALSE,
        needs_review BOOLEAN DEFAULT FALSE,
        submitted_by UUID,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    """
    
    try:
        # Note: Direct SQL execution might not be available through the Python client
        # We'll try to create the table by inserting a test record and handling the error
        test_record = {
            'name': 'Test Fragrance',
            'brand': 'Test Brand',
            'is_user_submitted': False,
            'needs_review': False
        }
        
        result = supabase.table('fragrances').insert(test_record).execute()
        # If successful, delete the test record
        supabase.table('fragrances').delete().eq('name', 'Test Fragrance').execute()
        logger.info("Table created successfully")
        return True
        
    except Exception as e:
        logger.error(f"Could not create table: {str(e)}")
        logger.info("Please create the table manually using the SQL schema provided")
        return False

def batch_insert_fragrances(supabase: Client, fragrances: List[Dict[str, Any]], batch_size: int = 100):
    """Insert fragrances in batches to avoid API limits."""
    total_inserted = 0
    total_errors = 0
    
    for i in range(0, len(fragrances), batch_size):
        batch = fragrances[i:i + batch_size]
        try:
            result = supabase.table('fragrances').insert(batch).execute()
            total_inserted += len(batch)
            logger.info(f"Inserted batch {i//batch_size + 1}: {len(batch)} fragrances (Total: {total_inserted})")
            
            # Add a small delay to avoid rate limiting
            time.sleep(0.2)
            
        except Exception as e:
            total_errors += len(batch)
            logger.error(f"Error inserting batch {i//batch_size + 1}: {str(e)}")
            
            # Try inserting individual records in this batch
            for fragrance in batch:
                try:
                    supabase.table('fragrances').insert(fragrance).execute()
                    total_inserted += 1
                    time.sleep(0.05)  # Small delay between individual inserts
                except Exception as individual_error:
                    total_errors += 1
                    logger.error(f"Error inserting fragrance '{fragrance.get('name', 'Unknown')}' by '{fragrance.get('brand', 'Unknown')}': {str(individual_error)}")
    
    return total_inserted, total_errors

def remove_duplicates(fragrances: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Remove duplicate fragrances based on name and brand."""
    seen = set()
    unique_fragrances = []
    
    for fragrance in fragrances:
        name = fragrance.get('name', '').lower().strip()
        brand = fragrance.get('brand', '').lower().strip()
        key = (name, brand)
        
        if key not in seen and name and brand:
            seen.add(key)
            unique_fragrances.append(fragrance)
    
    logger.info(f"Removed {len(fragrances) - len(unique_fragrances)} duplicates")
    return unique_fragrances

def main():
    """Main function to set up database and import fragrance data."""
    try:
        # Initialize Supabase client
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        logger.info("Connected to Supabase")
        
        # Create table if it doesn't exist
        if not create_table_if_not_exists(supabase):
            logger.error("Could not create table. Please run the SQL schema manually first.")
            return
        
        # Check if data already exists
        try:
            existing_count = supabase.table('fragrances').select('id', count='exact').execute()
            count = existing_count.count if hasattr(existing_count, 'count') else 0
            logger.info(f"Found {count} existing fragrances in database")
            
            if count > 1000:  # Assuming we have significant data already
                response = input(f"Database already contains {count} fragrances. Continue with import? (y/N): ")
                if response.lower() != 'y':
                    logger.info("Import cancelled by user")
                    return
        except Exception as e:
            logger.info("Could not check existing data count")
        
        # Read CSV file
        logger.info("Reading CSV file...")
        df = pd.read_csv('consolidated_perfume_data.csv')
        logger.info(f"Loaded {len(df)} rows from CSV")
        
        # Process the data
        logger.info("Processing fragrance data...")
        fragrances = []
        
        for index, row in df.iterrows():
            fragrance = process_fragrance_row(row)
            
            # Only add fragrances with valid name and brand
            if fragrance['name'] and fragrance['brand']:
                fragrances.append(fragrance)
            
            if (index + 1) % 10000 == 0:
                logger.info(f"Processed {index + 1} rows...")
        
        logger.info(f"Processed {len(fragrances)} valid fragrances")
        
        # Remove duplicates
        fragrances = remove_duplicates(fragrances)
        
        # Insert data into Supabase
        logger.info("Starting data import to Supabase...")
        total_inserted, total_errors = batch_insert_fragrances(supabase, fragrances, batch_size=50)
        
        logger.info(f"Import completed!")
        logger.info(f"Total inserted: {total_inserted}")
        logger.info(f"Total errors: {total_errors}")
        logger.info(f"Success rate: {(total_inserted / len(fragrances) * 100):.2f}%")
        
    except Exception as e:
        logger.error(f"Import failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
