"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { TrendingUp, Sparkles, ShoppingBag, Star, Heart, ExternalLink } from "lucide-react"

export default function Discover() {
  const [likedItems, setLikedItems] = useState<Set<string>>(new Set())

  const toggleLike = (id: string) => {
    const newLikedItems = new Set(likedItems)
    if (newLikedItems.has(id)) {
      newLikedItems.delete(id)
    } else {
      newLikedItems.add(id)
    }
    setLikedItems(newLikedItems)
  }

  const trendingFragrances = [
    {
      id: "1",
      name: "<PERSON><PERSON><PERSON> <PERSON><PERSON>",
      brand: "<PERSON> Dior",
      interest: "+38% interest",
      rating: 4.8,
      price: "$89",
      notes: ["Bergamot", "Pepper", "Ambroxan"],
      tags: ["Fresh", "Masculine", "Versatile"]
    },
    {
      id: "2", 
      name: "Aventus Creed",
      brand: "Creed",
      interest: "+34% interest",
      rating: 4.9,
      price: "$445",
      notes: ["Pineapple", "Birch", "Musk"],
      tags: ["Luxury", "Fruity", "Smoky"]
    },
    {
      id: "3",
      name: "Black Opium YSL",
      brand: "Yves Saint Laurent",
      interest: "+31% interest", 
      rating: 4.7,
      price: "$98",
      notes: ["Coffee", "Vanilla", "White Flowers"],
      tags: ["Sweet", "Feminine", "Evening"]
    }
  ]

  const newReleases = [
    {
      id: "4",
      name: "Libre Intense",
      brand: "Yves Saint Laurent",
      releaseDate: "2024-01-15",
      price: "$120",
      description: "A bold interpretation of the original Libre fragrance",
      notes: ["Lavender", "Orange Blossom", "Vanilla"],
      tags: ["New", "Floral", "Intense"]
    },
    {
      id: "5",
      name: "Oud Wood Intense",
      brand: "Tom Ford",
      releaseDate: "2024-02-01",
      price: "$350",
      description: "An intensified version of the beloved Oud Wood",
      notes: ["Oud", "Sandalwood", "Rosewood"],
      tags: ["New", "Woody", "Luxury"]
    },
    {
      id: "6",
      name: "Aqua Celestia Forte",
      brand: "Maison Francis Kurkdjian",
      releaseDate: "2024-01-30",
      price: "$285",
      description: "A stronger concentration of the ethereal Aqua Celestia",
      notes: ["Mimosa", "Cassis", "Musk"],
      tags: ["New", "Fresh", "Aquatic"]
    }
  ]

  const buyRecommendations = [
    {
      id: "7",
      category: "Best Value",
      name: "Dylan Blue",
      brand: "Versace",
      originalPrice: "$75",
      salePrice: "$52",
      discount: "30% off",
      reason: "Great performance for the price",
      retailer: "FragranceX",
      inStock: true
    },
    {
      id: "8", 
      category: "Trending Now",
      name: "Baccarat Rouge 540",
      brand: "Maison Francis Kurkdjian",
      originalPrice: "$325",
      salePrice: "$285",
      discount: "12% off",
      reason: "Most searched fragrance this month",
      retailer: "Sephora",
      inStock: true
    },
    {
      id: "9",
      category: "Limited Edition",
      name: "Tobacco Vanille",
      brand: "Tom Ford",
      originalPrice: "$250",
      salePrice: "$225",
      discount: "10% off",
      reason: "Limited holiday edition",
      retailer: "Nordstrom",
      inStock: false
    }
  ]

  return (
    <div className="space-y-6">
      <Tabs defaultValue="trending" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="trending">Trending</TabsTrigger>
          <TabsTrigger value="new">New</TabsTrigger>
          <TabsTrigger value="buy">Buy</TabsTrigger>
        </TabsList>

        <TabsContent value="trending" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-orange-500" />
                Hot This Week
              </CardTitle>
              <CardDescription>Most popular fragrances in the community</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {trendingFragrances.map((fragrance, index) => (
                <div key={fragrance.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 font-semibold">
                        {index + 1}
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm">{fragrance.name}</h4>
                        <p className="text-xs text-gray-600">{fragrance.brand}</p>
                        <p className="text-xs text-green-600 font-medium">{fragrance.interest}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleLike(fragrance.id)}
                        className="h-8 w-8 p-0"
                      >
                        <Heart 
                          className={`h-4 w-4 ${likedItems.has(fragrance.id) ? 'fill-red-500 text-red-500' : 'text-gray-400'}`} 
                        />
                      </Button>
                      <Button size="sm" variant="outline">
                        View
                      </Button>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <span className="text-xs font-medium">{fragrance.rating}</span>
                    </div>
                    <span className="text-xs text-gray-500">•</span>
                    <span className="text-xs font-semibold text-green-600">{fragrance.price}</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {fragrance.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs px-2 py-0">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="new" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-blue-500" />
                Latest Releases
              </CardTitle>
              <CardDescription>Fresh arrivals and new launches</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {newReleases.map((release) => (
                <div key={release.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <h4 className="font-semibold text-sm">{release.name}</h4>
                      <p className="text-xs text-gray-600">{release.brand}</p>
                      <p className="text-xs text-blue-600 font-medium">
                        Released {new Date(release.releaseDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleLike(release.id)}
                        className="h-8 w-8 p-0"
                      >
                        <Heart 
                          className={`h-4 w-4 ${likedItems.has(release.id) ? 'fill-red-500 text-red-500' : 'text-gray-400'}`} 
                        />
                      </Button>
                      <Button size="sm" variant="outline">
                        Learn More
                      </Button>
                    </div>
                  </div>
                  <p className="text-xs text-gray-700">{release.description}</p>
                  <div className="flex items-center gap-2">
                    <span className="text-xs font-semibold text-green-600">{release.price}</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {release.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs px-2 py-0">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="buy" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShoppingBag className="h-5 w-5 text-green-500" />
                Smart Shopping
              </CardTitle>
              <CardDescription>Best deals and purchase recommendations</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {buyRecommendations.map((item) => (
                <div key={item.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" className="text-xs">
                          {item.category}
                        </Badge>
                        {!item.inStock && (
                          <Badge variant="destructive" className="text-xs">
                            Out of Stock
                          </Badge>
                        )}
                      </div>
                      <h4 className="font-semibold text-sm">{item.name}</h4>
                      <p className="text-xs text-gray-600">{item.brand}</p>
                      <p className="text-xs text-gray-700 mt-1">{item.reason}</p>
                    </div>
                    <Button 
                      size="sm" 
                      variant={item.inStock ? "default" : "secondary"}
                      disabled={!item.inStock}
                      className="flex items-center gap-1"
                    >
                      <ExternalLink className="h-3 w-3" />
                      {item.inStock ? "Buy Now" : "Notify Me"}
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-gray-500 line-through">{item.originalPrice}</span>
                    <span className="text-sm font-semibold text-green-600">{item.salePrice}</span>
                    <Badge variant="secondary" className="text-xs">
                      {item.discount}
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-600">Available at {item.retailer}</p>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
