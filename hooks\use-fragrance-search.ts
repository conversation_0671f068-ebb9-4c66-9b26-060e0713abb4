import { useState, useEffect, useCallback } from 'react'
import { searchFragrances, type Fragrance } from '@/lib/supabase'

interface UseFragranceSearchResult {
  suggestions: Fragrance[]
  isLoading: boolean
  error: string | null
  searchTerm: string
  setSearchTerm: (term: string) => void
  clearSuggestions: () => void
}

export function useFragranceSearch(
  debounceMs: number = 300,
  minSearchLength: number = 2
): UseFragranceSearchResult {
  const [searchTerm, setSearchTerm] = useState('')
  const [suggestions, setSuggestions] = useState<Fragrance[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Debounced search function
  const debouncedSearch = useCallback(
    async (term: string) => {
      if (term.length < minSearchLength) {
        setSuggestions([])
        setIsLoading(false)
        return
      }

      setIsLoading(true)
      setError(null)

      try {
        const { data, error: searchError } = await searchFragrances(term, 8) // Limit to 8 suggestions

        if (searchError) {
          setError('Failed to search fragrances')
          setSuggestions([])
        } else {
          setSuggestions(data || [])
        }
      } catch (err) {
        setError('An unexpected error occurred')
        setSuggestions([])
      } finally {
        setIsLoading(false)
      }
    },
    [minSearchLength]
  )

  // Debounce effect
  useEffect(() => {
    const timer = setTimeout(() => {
      debouncedSearch(searchTerm)
    }, debounceMs)

    return () => clearTimeout(timer)
  }, [searchTerm, debounceMs, debouncedSearch])

  const clearSuggestions = useCallback(() => {
    setSuggestions([])
    setError(null)
  }, [])

  return {
    suggestions,
    isLoading,
    error,
    searchTerm,
    setSearchTerm,
    clearSuggestions
  }
}
