-- MyScentSpace Fragrances Database Schema
-- Execute this script in your Supabase SQL Editor

-- 1. Create the fragrances table
CREATE TABLE IF NOT EXISTS fragrances (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    brand TEXT NOT NULL,
    release_year INTEGER,
    gender TEXT,
    top_notes TEXT,
    middle_notes TEXT,
    base_notes TEXT,
    main_accords TEXT,
    image_url TEXT,
    is_user_submitted BOOLEAN DEFAULT FALSE,
    needs_review BOOLEAN DEFAULT FALSE,
    submitted_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create indexes for efficient searching
CREATE INDEX IF NOT EXISTS idx_fragrances_name ON fragrances USING gin(to_tsvector('english', name));
CREATE INDEX IF NOT EXISTS idx_fragrances_brand ON fragrances USING gin(to_tsvector('english', brand));
CREATE INDEX IF NOT EXISTS idx_fragrances_name_brand ON fragrances USING gin(to_tsvector('english', name || ' ' || brand));
CREATE INDEX IF NOT EXISTS idx_fragrances_top_notes ON fragrances USING gin(to_tsvector('english', COALESCE(top_notes, '')));
CREATE INDEX IF NOT EXISTS idx_fragrances_middle_notes ON fragrances USING gin(to_tsvector('english', COALESCE(middle_notes, '')));
CREATE INDEX IF NOT EXISTS idx_fragrances_base_notes ON fragrances USING gin(to_tsvector('english', COALESCE(base_notes, '')));
CREATE INDEX IF NOT EXISTS idx_fragrances_main_accords ON fragrances USING gin(to_tsvector('english', COALESCE(main_accords, '')));
CREATE INDEX IF NOT EXISTS idx_fragrances_gender ON fragrances(gender);
CREATE INDEX IF NOT EXISTS idx_fragrances_release_year ON fragrances(release_year);
CREATE INDEX IF NOT EXISTS idx_fragrances_user_submitted ON fragrances(is_user_submitted);
CREATE INDEX IF NOT EXISTS idx_fragrances_needs_review ON fragrances(needs_review);

-- 3. Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 4. Create trigger to automatically update updated_at
CREATE TRIGGER update_fragrances_updated_at 
    BEFORE UPDATE ON fragrances 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 5. Enable Row Level Security (RLS)
ALTER TABLE fragrances ENABLE ROW LEVEL SECURITY;

-- 6. Create RLS policies

-- Allow everyone to read all fragrances (for searching)
CREATE POLICY "Allow public read access" ON fragrances
    FOR SELECT USING (true);

-- Allow authenticated users to insert user-submitted fragrances
CREATE POLICY "Allow authenticated users to insert" ON fragrances
    FOR INSERT WITH CHECK (
        auth.role() = 'authenticated' AND 
        is_user_submitted = true AND
        submitted_by = auth.uid()
    );

-- Allow users to update their own submitted fragrances
CREATE POLICY "Allow users to update own submissions" ON fragrances
    FOR UPDATE USING (
        auth.role() = 'authenticated' AND 
        is_user_submitted = true AND
        submitted_by = auth.uid()
    );

-- Allow admins to update any fragrance (you'll need to set up admin roles)
-- CREATE POLICY "Allow admin full access" ON fragrances
--     FOR ALL USING (
--         auth.role() = 'authenticated' AND 
--         auth.jwt() ->> 'role' = 'admin'
--     );
