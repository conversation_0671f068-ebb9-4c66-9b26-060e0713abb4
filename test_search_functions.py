#!/usr/bin/env python3
"""
Test script to verify the search functions are working correctly
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv
import logging

# Load environment variables from .env.local
load_dotenv('.env.local')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Supabase configuration
SUPABASE_URL = os.getenv("NEXT_PUBLIC_SUPABASE_URL")
SUPABASE_KEY = os.getenv("NEXT_PUBLIC_SUPABASE_ANON_KEY")

def test_search_functions():
    """Test the search functions to ensure they work correctly."""
    try:
        # Initialize Supabase client
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        logger.info("Connected to Supabase")
        
        # Test 1: Search for Dior fragrances
        logger.info("Test 1: Searching for 'Dior' fragrances...")
        result = supabase.rpc('search_fragrances', {
            'search_term': 'Dior',
            'limit_count': 5,
            'offset_count': 0
        }).execute()
        
        if result.data:
            logger.info(f"✅ Found {len(result.data)} Dior fragrances")
            for fragrance in result.data[:3]:
                logger.info(f"  - {fragrance['name']} by {fragrance['brand']}")
        else:
            logger.error("❌ No Dior fragrances found")
        
        # Test 2: Search for Sauvage
        logger.info("\nTest 2: Searching for 'Sauvage'...")
        result = supabase.rpc('search_fragrances', {
            'search_term': 'Sauvage',
            'limit_count': 5,
            'offset_count': 0
        }).execute()
        
        if result.data:
            logger.info(f"✅ Found {len(result.data)} Sauvage fragrances")
            for fragrance in result.data[:3]:
                logger.info(f"  - {fragrance['name']} by {fragrance['brand']}")
        else:
            logger.error("❌ No Sauvage fragrances found")
        
        # Test 3: Search for Tom Ford
        logger.info("\nTest 3: Searching for 'Tom Ford'...")
        result = supabase.rpc('search_fragrances', {
            'search_term': 'Tom Ford',
            'limit_count': 5,
            'offset_count': 0
        }).execute()
        
        if result.data:
            logger.info(f"✅ Found {len(result.data)} Tom Ford fragrances")
            for fragrance in result.data[:3]:
                logger.info(f"  - {fragrance['name']} by {fragrance['brand']}")
        else:
            logger.error("❌ No Tom Ford fragrances found")
        
        # Test 4: Search for non-existent fragrance
        logger.info("\nTest 4: Searching for 'xyz123nonexistent'...")
        result = supabase.rpc('search_fragrances', {
            'search_term': 'xyz123nonexistent',
            'limit_count': 5,
            'offset_count': 0
        }).execute()
        
        if not result.data or len(result.data) == 0:
            logger.info("✅ Correctly returned no results for non-existent fragrance")
        else:
            logger.warning(f"⚠️ Unexpected results for non-existent fragrance: {len(result.data)} results")
        
        # Test 5: Check total fragrance count
        logger.info("\nTest 5: Checking total fragrance count...")
        result = supabase.table('fragrances').select('id', count='exact').limit(1).execute()
        
        if hasattr(result, 'count') and result.count:
            logger.info(f"✅ Total fragrances in database: {result.count}")
        else:
            # Alternative method to count
            result = supabase.table('fragrances').select('id').execute()
            if result.data:
                logger.info(f"✅ Total fragrances in database: {len(result.data)}")
            else:
                logger.error("❌ Could not determine fragrance count")
        
        logger.info("\n🎉 Search function tests completed!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        raise

if __name__ == "__main__":
    test_search_functions()
