# MyScentSpace Fragrance Database Implementation Guide

This guide will help you set up a complete fragrance database for the MyScentSpace project using Supabase.

## Overview

The database will contain:
- **88,000+ fragrances** from the consolidated_perfume_data.csv file
- **Full-text search** capabilities for names, brands, and notes
- **User-submitted fragrances** with admin review system
- **Row Level Security** for data protection
- **Optimized indexes** for fast searching

## Prerequisites

1. Active Supabase project
2. Python 3.8+ (for data import)
3. Access to consolidated_perfume_data.csv file

## Step 1: Activate Your Supabase Project

1. Go to [supabase.com](https://supabase.com) and log into your dashboard
2. Navigate to your project "nailsbylingg's Project"
3. Click on the project to activate it (may take a few minutes)
4. Wait for the status to change from "INACTIVE" to "ACTIVE"

## Step 2: Create Database Schema

1. In your Supabase dashboard, go to the **SQL Editor**
2. Copy and paste the contents of `supabase_fragrances_schema.sql`
3. Click **Run** to execute the schema creation
4. Copy and paste the contents of `supabase_search_functions.sql`
5. Click **Run** to create the search functions

## Step 3: Import Fragrance Data

### Option A: Using Python Script (Recommended)

1. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Create your environment file:
   ```bash
   cp .env.example .env
   ```

3. Edit `.env` with your Supabase credentials:
   - Get your URL and anon key from Supabase Project Settings > API
   - Update the values in `.env`

4. Run the import script:
   ```bash
   python import_fragrances_data.py
   ```

### Option B: Manual CSV Import (Alternative)

1. In Supabase dashboard, go to **Table Editor**
2. Select the `fragrances` table
3. Click **Insert** > **Import data from CSV**
4. Upload `consolidated_perfume_data.csv`
5. Map the columns appropriately

## Step 4: Verify the Setup

1. Go to **Table Editor** > `fragrances`
2. You should see thousands of fragrance records
3. Test the search function in SQL Editor:
   ```sql
   SELECT * FROM search_fragrances('Dior Sauvage', 10, 0);
   ```

## Step 5: API Usage Examples

### Search Fragrances
```javascript
// Search by name/brand
const { data, error } = await supabase
  .rpc('search_fragrances', {
    search_term: 'Dior Sauvage',
    limit_count: 20,
    offset_count: 0
  });

// Search by notes
const { data, error } = await supabase
  .rpc('search_fragrances_by_notes', {
    search_notes: 'bergamot',
    limit_count: 20
  });
```

### Add User-Submitted Fragrance
```javascript
const { data, error } = await supabase
  .rpc('add_user_fragrance', {
    fragrance_name: 'My Custom Fragrance',
    fragrance_brand: 'Custom Brand',
    fragrance_image_url: 'https://example.com/image.jpg',
    fragrance_release_year: 2024,
    fragrance_gender: 'Unisex'
  });
```

### Get Fragrances for Review (Admin)
```javascript
const { data, error } = await supabase
  .rpc('get_fragrances_for_review', {
    limit_count: 50
  });
```

## Database Schema Details

### Fragrances Table Structure
- `id` (UUID, Primary Key)
- `name` (Text, Required) - Fragrance name
- `brand` (Text, Required) - Brand name
- `release_year` (Integer, Optional) - Year released
- `gender` (Text, Optional) - Target gender
- `top_notes` (Text, Optional) - Top notes description
- `middle_notes` (Text, Optional) - Middle notes description
- `base_notes` (Text, Optional) - Base notes description
- `main_accords` (Text, Optional) - Main accords
- `image_url` (Text, Optional) - Fragrance image URL
- `is_user_submitted` (Boolean) - Flag for user submissions
- `needs_review` (Boolean) - Flag for admin review
- `submitted_by` (UUID) - Reference to user who submitted
- `created_at` (Timestamp) - Creation timestamp
- `updated_at` (Timestamp) - Last update timestamp

### Indexes for Performance
- Full-text search indexes on name, brand, and notes
- Regular indexes on gender, release_year, and flags
- Composite index for name + brand searches

### Security Features
- Row Level Security (RLS) enabled
- Public read access for searching
- Authenticated users can submit fragrances
- Users can only edit their own submissions
- Admin policies ready for implementation

## User Flow Implementation

1. **Search Existing Fragrances**: Users search the database when adding to their collection
2. **Submit New Fragrance**: If not found, users can submit new entries
3. **Admin Review**: User submissions are flagged for review
4. **Approval Process**: Admins can add missing notes and approve entries

## Next Steps

1. Implement user authentication in your app
2. Create admin role management
3. Build the search UI components
4. Add image upload functionality
5. Implement the review/approval workflow

## Troubleshooting

- **Import fails**: Check your .env file has correct Supabase credentials
- **Search not working**: Verify the search functions were created successfully
- **Permission errors**: Ensure RLS policies are set up correctly
- **Slow queries**: Check that indexes were created properly

## Support

If you encounter issues:
1. Check Supabase logs in the dashboard
2. Verify your API keys and permissions
3. Test queries in the SQL Editor first
4. Check the browser console for client-side errors
